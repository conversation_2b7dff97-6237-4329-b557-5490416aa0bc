/*
 * -------------------------------------------------------
 * SECTION: Base Permissions
 * -------------------------------------------------------
 */

-- We remove all default privileges from public schema on functions to prevent public access to them
alter default privileges revoke execute on functions from public;

revoke all on schema public from public;

revoke all privileges on database "postgres" from "anon";

revoke all privileges on schema "public" from "anon";

revoke all privileges on schema "storage" from "anon";

revoke all privileges on all sequences in schema "public" from "anon";

revoke all privileges on all sequences in schema "storage" from "anon";

revoke all privileges on all functions in schema "public" from "anon";

revoke all privileges on all functions in schema "storage" from "anon";

revoke all privileges on all tables in schema "public" from "anon";

revoke all privileges on all tables in schema "storage" from "anon";

-- We remove all default privileges from public schema on functions to prevent public access to them by default
alter default privileges in schema public revoke execute on functions from anon;

-- We allow the authenticated role to execute functions in the public schema
grant usage on schema public to authenticated, service_role;
grant execute on all functions in schema public to authenticated, service_role;

/*
 * -------------------------------------------------------
 * SECTION: Functions
 * -------------------------------------------------------
 */
create or replace function create_user_record() 
  returns trigger language plpgsql security definer as $$ 
  begin
    if new.raw_app_meta_data->>'provider' != 'google' then
      insert into public.users (id)
      values (new.id);
    end if;
    return new;
  end;
$$;

create trigger insert_database_user after insert on auth.users 
for each row execute function create_user_record();


create or replace function has_permission (org_id uuid, permission_name text, _user_id uuid default auth.uid())
  returns boolean language plpgsql security definer set search_path = '' as $$
  begin
    return exists (
      select 1 from public.membership m
      join public.role_permission rp on m.role_id = rp.role_id
      join public.permission p on rp.permission_id = p.id
      where m.user_id = _user_id
        and m.organization_id = org_id
        and p.name = permission_name
    );
  end;
$$;

create or replace function get_current_user_organization_id () 
  returns uuid language sql security definer set search_path = '' as $$
  select organization_id from public.membership where user_id = auth.uid() limit 1;
$$;

/*
 * -----------------------------------------------------------------------------
 * SECTION: Storage Buckets
 * -----------------------------------------------------------------------------
 */

insert into storage.buckets (id, name, public) values ('logos', 'logos', true);

insert into storage.buckets (id, name, public) values ('avatars', 'avatars', true);

insert into storage.buckets (id, name, public) values ('feedback', 'feedback', true);

insert into storage.buckets (id, name, public) values ('attachments', 'attachments', true);

/*
 * -----------------------------------------------------------------------------
 * SECTION: Buckets RLS Policies 
 * -----------------------------------------------------------------------------
 */

-- Storage Logo Bucket Policies
create policy "Logos can be read by any user" on storage.objects 
for select using (bucket_id = 'logos');

create policy "Logos can be inserted only by the authenticated users" on storage.objects 
for insert to authenticated with check (bucket_id = 'logos' and auth.uid() is not null);

create policy "Logos can be updated only by the permitted users" on storage.objects 
for update to authenticated using (bucket_id = 'logos' and has_permission(get_current_user_organization_id(), 'manage_organization'));

create policy "Logos can be deleted only by the permitted users" on storage.objects 
for delete to authenticated using (bucket_id = 'logos' and has_permission(get_current_user_organization_id(), 'manage_organization'));

-- Storage Avatar Bucket Policies
create policy "Avatars can be read by any user" on storage.objects 
for select using (bucket_id = 'avatars');

create policy "Avatars can be inserted only by the authenticated users" on storage.objects 
for insert to authenticated with check (bucket_id = 'avatars' and auth.uid() is not null);

create policy "Avatars can be updated only by the owner" on storage.objects 
for update to authenticated using (bucket_id = 'avatars' and owner_id = auth.uid()::text);

create policy "Avatars can be deleted only by the owner" on storage.objects 
for delete to authenticated using (bucket_id = 'avatars' and owner_id = auth.uid()::text);

-- Storage Feedback Bucket Policies
create policy "Allow public read access to files" on storage.objects 
for select using (bucket_id = 'feedback');

create policy "Feedback file can be inserted by the user that owns the file" on storage.objects
for insert with check (bucket_id = 'feedback' and (split_part(storage.filename(name), '_', 1)::uuid) = auth.uid ());

create policy "Feedback file can be deleted by the user that owns the file" on storage.objects
for delete to authenticated using (bucket_id = 'feedback' and (split_part(storage.filename(name), '_', 1)::uuid) = auth.uid ());

-- Storage Attachment Bucket Policies
create policy "Attachments can be read by any user" on storage.objects 
for select using (bucket_id = 'attachments' and auth.uid() is not null);

create policy "Attachments can be inserted only by the authenticated users" on storage.objects 
for insert to authenticated with check (bucket_id = 'attachments' and auth.uid() is not null);


/*
 * -----------------------------------------------------------------------------
 * SECTION: Enable Realtime 
 * -----------------------------------------------------------------------------
 */
alter publication supabase_realtime add table public.notification;
alter publication supabase_realtime add table public.chat_room_member;

/*
 * -----------------------------------------------------------------------------
 * SECTION: Cron Jobs 
 * -----------------------------------------------------------------------------
 */
create extension if not exists pg_cron;

select cron.schedule('delete-expired-notifications', '0 0 * * 0', $$
  delete from public.notification
  where expires_at < current_timestamp
      or is_dismissed = true;
$$);

select cron.schedule('delete-expired-review-requests', '0 0 * * 0', $$
  delete from user_review_request
  where expiration_date is not null
    and expiration_date < current_timestamp;
$$);