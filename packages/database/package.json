{"name": "@packages/database", "version": "0.0.0", "private": true, "main": "./src/index.ts", "scripts": {"clean": "rm -rf .turbo node_modules", "lint": "biome lint", "format": "biome format --write .", "typecheck": "tsc --noEmit", "generate": "drizzle-kit generate --name=migration"}, "peerDependencies": {"@types/node": ">=20.0.0"}, "dependencies": {"@packages/supabase": "workspace:*", "@packages/utils": "workspace:*", "dotenv": "^16.4.5", "drizzle-orm": "^0.41.0", "jwt-decode": "^4.0.0", "postgres": "^3.4.5"}, "devDependencies": {"drizzle-kit": "^0.30.5", "typescript": "^5"}, "exports": {".": "./src/index.ts", "./tables": "./src/schema/tables.ts", "./auth": "./src/schema/auth.ts", "./functions": "./src/functions/index.ts", "./enums": "./src/schema/enums.ts", "./types": "./src/schema/types.ts", "./drizzle-orm": "./src/orm.ts"}}