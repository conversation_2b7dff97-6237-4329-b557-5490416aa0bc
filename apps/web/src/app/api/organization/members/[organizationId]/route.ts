import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { getMembersAndInvitationByOrgId } from '@packages/database/functions';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, ctx) => {
  const { organizationId } = await ctx.params;
  const parsedOrganizationId = parseAsUuid.parseServerSide(organizationId);

  if (!parsedOrganizationId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const isClient = ctx.authUser.app_metadata.isClient === true;
  if (!isClient) {
    logger.error('❌ USER IS NOT A CLIENT');
    return Response.json(
      { error: 'USER_IS_NOT_A_CLIENT' },
      {
        status: 403,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const members = await getMembersAndInvitationByOrgId(db, {
    organizationId: parsedOrganizationId,
  });

  if (!members) {
    logger.error('❌ MEMBERS NOT FOUND');
    return Response.json(
      { error: 'MEMBERS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(members);
});
