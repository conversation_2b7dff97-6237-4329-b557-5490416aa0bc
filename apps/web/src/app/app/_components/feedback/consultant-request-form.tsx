'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useContext, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { Button } from '@packages/ui/button';
import { Form } from '@packages/ui/form';
import { FormTextarea } from '@packages/ui/form-textarea';
import { IndustrySelect } from './industry-select';

import AppContainerContext from '@/core/contexts/app-container-context';
import {
  type ConsultantRequestFormData,
  consultantRequestFormSchema,
} from '@/core/schemas/feedback';
import { createConsultantRequestAction } from '@/core/server/actions/feedback';

export function ConsultantRequestForm() {
  const [isLoading, setIsLoading] = useState(false);
  const { setShowFeedback } = useContext(AppContainerContext);

  const form = useForm<ConsultantRequestFormData>({
    resolver: zod<PERSON>esolver(consultantRequestFormSchema),
    defaultValues: {
      industries: [],
      description: '',
    },
  });

  const onSubmit = async (data: ConsultantRequestFormData) => {
    setIsLoading(true);
    const promise = createConsultantRequestAction(data);

    toast.promise(promise, {
      loading: 'Submitting your consultant request...',
      success: () => {
        setShowFeedback(false);
        return 'Successfully submitted your consultant request!';
      },
      error: 'Something went wrong. Please try again.',
      finally: () => setIsLoading(false),
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-6">
        <IndustrySelect
          form={form}
          fieldName="industries"
          disabled={isLoading}
        />

        <FormTextarea
          form={form}
          fieldName="description"
          fieldLabel="What type of consulting services are you looking for?"
          placeholder="Please describe your consulting needs, including any specific requirements or challenges you'd like help with."
          className="resize-none min-h-52"
          disabled={isLoading}
          maxRows={10}
          required
        />

        <Button type="submit" className="w-full" disabled={isLoading}>
          Submit
        </Button>
      </form>
    </Form>
  );
}
