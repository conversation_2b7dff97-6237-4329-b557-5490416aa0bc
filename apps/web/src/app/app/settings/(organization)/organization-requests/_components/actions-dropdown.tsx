import type { GetOrganizationJoinRequestsResponse } from '@packages/database/functions';

import { EllipsisVerticalIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import { toast } from 'sonner';

import { MembershipRole } from '@packages/database/enums';
import { Button } from '@packages/ui/button';
import { ConfirmModal } from '@packages/ui/confirm-modal';
import { DeleteModal } from '@packages/ui/delete-modal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@packages/ui/dropdown-menu';

import { SiteConfig } from '@/configuration';
import {
  approveOrganizationJoinRequestsAction,
  rejectOrganizationJoinRequestsAction,
} from '@/core/server/actions/organization-join-request';

const APPROVE_REQUEST_TITLE = 'Approve Request';
const APPROVE_REQUEST_DESCRIPTION =
  'Are you sure you want to approve this request? This action cannot be undone. The user will be notified.';

const REJECT_REQUEST_TITLE = 'Reject Request';
const REJECT_REQUEST_DESCRIPTION =
  'Are you sure you want to reject this request? This action cannot be undone. The user will be notified.';

interface ActionsDropdownProps {
  organizationJoinRequest: GetOrganizationJoinRequestsResponse;
  onSuccess?: VoidFunction;
}

export function ActionsDropdown({
  organizationJoinRequest,
  onSuccess,
}: ActionsDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { id: orgJoinRequestId, organizationId } = organizationJoinRequest;

  const handleApproveRequest = (roleId: MembershipRole) => {
    const promise = approveOrganizationJoinRequestsAction({
      orgJoinRequestIds: [orgJoinRequestId],
      organizationId,
      roleId,
    });
    toast.promise(promise, {
      loading: 'Approving request...',
      success: () => {
        setIsOpen(false);
        if (onSuccess) {
          onSuccess();
        }
        return 'Request approved';
      },
      error: (error) => {
        if (!SiteConfig.isProduction) {
          console.error({ error });
        }
        return 'Failed to approve request';
      },
    });
  };

  const handleRejectRequest = () => {
    const promise = rejectOrganizationJoinRequestsAction({
      orgJoinRequestIds: [orgJoinRequestId],
    });
    toast.promise(promise, {
      loading: 'Rejecting request...',
      success: () => {
        setIsOpen(false);
        if (onSuccess) {
          onSuccess();
        }
        return 'Request rejected';
      },
      error: (error) => {
        if (!SiteConfig.isProduction) {
          console.error({ error });
        }
        return 'Failed to reject request';
      },
    });
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          size="icon"
          variant="ghost"
          className="size-8 text-muted-foreground hover:text-foreground"
        >
          <EllipsisVerticalIcon className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent side="left" align="start">
        <ConfirmModal
          title={APPROVE_REQUEST_TITLE}
          description={APPROVE_REQUEST_DESCRIPTION}
          onConfirm={() => handleApproveRequest(MembershipRole.Member)}
          confirmText="Approve as Employee"
        >
          <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
            Approve as Employee
          </DropdownMenuItem>
        </ConfirmModal>
        <ConfirmModal
          title={APPROVE_REQUEST_TITLE}
          description={APPROVE_REQUEST_DESCRIPTION}
          onConfirm={() => handleApproveRequest(MembershipRole.Admin)}
          confirmText="Approve as Admin"
        >
          <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
            Approve as Admin
          </DropdownMenuItem>
        </ConfirmModal>

        <DropdownMenuSeparator className="mx-2" />

        <DeleteModal
          title={REJECT_REQUEST_TITLE}
          description={REJECT_REQUEST_DESCRIPTION}
          onDelete={handleRejectRequest}
          Icon={XMarkIcon}
          deleteText="Reject Request"
        >
          <DropdownMenuItem
            onSelect={(e) => e.preventDefault()}
            className="text-destructive hover:!text-destructive"
          >
            Reject Request
          </DropdownMenuItem>
        </DeleteModal>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
