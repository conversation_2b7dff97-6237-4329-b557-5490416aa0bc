import type { GetOrganizationJoinRequestsResponse } from '@packages/database/functions';
import type { Table } from '@packages/ui/data-table/types';

import {
  CheckCircledIcon,
  Cross2Icon,
  CrossCircledIcon,
} from '@radix-ui/react-icons';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { toast } from 'sonner';

import { MembershipRole } from '@packages/database/enums';
import { Button } from '@packages/ui/button';
import { ConfirmModal } from '@packages/ui/confirm-modal';
import { DeleteModal } from '@packages/ui/delete-modal';
import { Separator } from '@packages/ui/separator';

import { MembershipRoleSelector } from '@/components/domain/organization/invite/membership-role-selector';
import { useUserSession } from '@/core/hooks/user';
import {
  approveOrganizationJoinRequestsAction,
  rejectOrganizationJoinRequestsAction,
} from '@/core/server/actions/organization-join-request';

interface OrganizationRequestsTableFloatingBarProps {
  table: Table<GetOrganizationJoinRequestsResponse>;
}

export function OrganizationRequestsTableFloatingBar({
  table,
}: OrganizationRequestsTableFloatingBarProps) {
  const router = useRouter();
  const rows = table.getFilteredSelectedRowModel().rows;
  const pendingRows = rows.filter((row) => row.original.status === 'PENDING');

  const emails = pendingRows.map((row) => row.original.email);
  const orgJoinRequestIds = pendingRows.map((row) => row.original.id);
  const hasProcessedRequests = rows.length !== pendingRows.length;
  const organizationId = rows.at(0)?.original.organizationId as string;

  const currentUser = useUserSession();
  const currentUserRole = currentUser?.user?.role;

  const [isPending, startTransition] = React.useTransition();
  const [method, setMethod] = React.useState<'approve' | 'reject'>();
  const [role, setRole] = React.useState<MembershipRole>(MembershipRole.Member);

  const handleApprove = () => {
    setMethod('approve');

    startTransition(() => {
      const promise = approveOrganizationJoinRequestsAction({
        orgJoinRequestIds,
        organizationId,
        roleId: role,
      });
      toast.promise(promise, {
        loading: 'Approving requests...',
        success: () => {
          table.toggleAllRowsSelected(false);
          router.refresh();
          return 'Successfully approved requests';
        },
        error: 'Failed to approve requests. Please try again.',
      });
    });
  };

  const handleReject = () => {
    setMethod('reject');

    startTransition(() => {
      const promise = rejectOrganizationJoinRequestsAction({
        orgJoinRequestIds,
      });
      toast.promise(promise, {
        loading: 'Rejecting requests...',
        success: () => {
          table.toggleAllRowsSelected(false);
          router.refresh();
          return 'Successfully rejected requests';
        },
        error: 'Failed to reject requests. Please try again.',
      });
    });
  };

  React.useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        table.toggleAllRowsSelected(false);
      }
    }

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [table]);

  return (
    <div className="fixed inset-x-0 bottom-20 sm:bottom-4 z-50 mx-auto w-fit px-4">
      <div className="w-full overflow-x-auto">
        <div className="mx-auto flex w-fit items-center gap-2 rounded-md border bg-card p-2 shadow-2xl">
          <div className="flex h-7 items-center rounded-md border border-dashed pl-2.5 pr-1">
            <span className="whitespace-nowrap text-xs">
              {rows.length} selected
            </span>
            <Separator orientation="vertical" className="ml-2 mr-1" />
            <Button
              size="icon"
              variant="ghost"
              className="size-5 hover:border"
              onClick={() => table.toggleAllRowsSelected(false)}
            >
              <Cross2Icon className="size-4" />
            </Button>
          </div>
          <Separator orientation="vertical" className="hidden h-5 sm:block" />
          <div className="flex items-center gap-1.5">
            <MembershipRoleSelector
              value={role}
              onChange={setRole}
              currentUserRole={currentUserRole}
              disabled={isPending || hasProcessedRequests}
            />

            <ConfirmModal
              title="Approve Requests"
              description={getApproveDescription(emails)}
              onConfirm={handleApprove}
              confirmText="Approve"
            >
              <Button
                size="sm"
                variant="secondary"
                loading={isPending && method === 'approve'}
                disabled={isPending || hasProcessedRequests}
                className="text-green-600"
              >
                <CheckCircledIcon className="size-4 mr-1" />
                <span className="hidden sm:inline-flex">Approve</span>
              </Button>
            </ConfirmModal>

            <DeleteModal
              title="Reject Requests"
              description={getRejectDescription(emails)}
              onDelete={handleReject}
              deleteText="Reject"
            >
              <Button
                size="sm"
                variant="secondary"
                className="text-red-600"
                loading={isPending && method === 'reject'}
                disabled={isPending || hasProcessedRequests}
              >
                <CrossCircledIcon className="size-4 mr-1" />
                <span className="hidden sm:inline-flex">Reject</span>
              </Button>
            </DeleteModal>
          </div>
        </div>
      </div>
    </div>
  );
}

function getApproveDescription(emails: string[]) {
  return (
    <span>
      By continuing, you will be approving the requests of the following users:
      <br />
      {emails.map((email) => (
        <strong key={email} className="flex">
          {email}
        </strong>
      ))}
    </span>
  );
}

function getRejectDescription(emails: string[]) {
  return (
    <span>
      Are you sure you want to reject these requests? This action cannot be
      undone. The user will be notified. By continuing, you will be rejecting
      the requests of the following users:
      <br />
      {emails.map((email) => (
        <strong key={email} className="flex">
          {email}
        </strong>
      ))}
    </span>
  );
}
