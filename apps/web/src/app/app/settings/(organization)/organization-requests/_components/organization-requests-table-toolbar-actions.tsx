'use client';

import type { OrganizationJoinRequestResponse } from '@packages/database/types';
import type { Table } from '@packages/ui/data-table/types';

import { TrashIcon } from '@heroicons/react/24/outline';
import { DownloadIcon, PlusIcon } from '@radix-ui/react-icons';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@packages/ui/button';
import { exportTableToCSV } from '@packages/ui/data-table/utils';
import { DeleteModal } from '@packages/ui/delete-modal';
import { If } from '@packages/ui/if';

import { InviteEmployeesSheet } from '@/components/domain/organization/invite/invite-employees-sheet';
import { IfHasPermissions } from '@/components/if-has-permissions';

import { canDeleteInvites } from '@/core/permissions';
import { deleteMultipleInvitationsAction } from '@/core/server/actions/invitation';

interface OrganizationRequestsTableToolbarActionsProps {
  table: Table<OrganizationJoinRequestResponse>;
}

export function OrganizationRequestsTableToolbarActions({
  table,
}: OrganizationRequestsTableToolbarActionsProps) {
  const router = useRouter();
  const [showInviteEmployees, setShowInviteEmployees] = useState(false);

  const selectedEmployees = table
    .getFilteredSelectedRowModel()
    .rows.map((row) => row.original);

  const invitationIds = selectedEmployees.map((item) => item.id);
  const memberEmails = selectedEmployees.map((item) => item.email);

  const onInviteDeleteRequested = useCallback(() => {
    table.toggleAllRowsSelected(false);
    router.refresh();
  }, [table, router]);

  const onInviteEmployees = () => {
    setShowInviteEmployees(true);
    table.toggleAllRowsSelected(false);
  };

  const handleExportCSV = () => {
    exportTableToCSV(table, {
      filename: 'organization-requests',
      excludeColumns: ['select', 'actions'],
    });
  };

  return (
    <div className="flex items-center gap-2">
      {/* <If condition={selectedEmployees.length > 0}>
        <IfHasPermissions condition={canDeleteInvites}>
          <MultipleInvitesDeleteButton
            invitationIds={invitationIds}
            memberEmails={memberEmails}
            onDelete={onInviteDeleteRequested}
          />
        </IfHasPermissions>
      </If> */}

      <Button variant="outline" size="sm" onClick={handleExportCSV}>
        <DownloadIcon className="mr-2 size-4" />
        <span>Export</span>
      </Button>

      <Button size="sm" onClick={onInviteEmployees}>
        <PlusIcon className="mr-2 size-4" />
        <span>Invite Employees</span>
      </Button>

      <InviteEmployeesSheet
        open={showInviteEmployees}
        onOpenChange={setShowInviteEmployees}
      />
    </div>
  );
}

// interface MultipleInvitesDeleteButtonProps {
//   invitationIds: number[];
//   memberEmails: string[];
//   onDelete?: VoidFunction;
// }

// function MultipleInvitesDeleteButton({
//   invitationIds,
//   memberEmails,
//   onDelete,
// }: MultipleInvitesDeleteButtonProps) {
//   const onInviteDeleteRequested = useCallback(() => {
//     const promise = deleteMultipleOrganizationRequestsAction({ invitationIds });
//     toast.promise(promise, {
//       loading: 'Deleting invitations...',
//       success: () => {
//         if (onDelete) {
//           onDelete();
//         }
//         return 'Successfully deleted invitations';
//       },
//       error: 'Failed to delete invitations. Please try again.',
//     });
//   }, [invitationIds, onDelete]);
//   return (
//     <DeleteModal
//       itemName="invitation"
//       onDelete={onInviteDeleteRequested}
//       description={
//         <>
//           <span>
//             This action cannot be undone. This will permanently delete and
//             remove your invitation from our servers.
//           </span>
//           <span className="inline-flex pt-4">
//             <span>You are deleting the invite to</span>&nbsp;
//             <strong>{memberEmails.join(', ')}</strong>
//           </span>
//         </>
//       }
//     >
//       <Button variant="outline" size="sm" type="button">
//         <TrashIcon className="size-4 mr-2" />
//         <span>Delete ({memberEmails.length})</span>
//       </Button>
//     </DeleteModal>
//   );
// }
