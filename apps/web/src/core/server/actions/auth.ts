'use server';

import type { AuthError } from '@supabase/supabase-js';

import { getSupabaseServerClient } from '@packages/supabase/server';
import { logger } from '@packages/utils/logger';
import { permanentRedirect, redirect } from 'next/navigation';

import { SiteConfig } from '@/configuration';
import {
  emailOtpFormSchema,
  emailPasswordSignInFormSchema,
  emailPasswordSignUpFormSchema,
  oAuthProviderFormSchema,
  requestPasswordResetFormSchema,
  resendLinkFormSchema,
  verifyMfaChallengeBodySchema,
  verifyOtpSchema,
} from '@/core/schemas/auth';
import { rateLimitedActionClient } from '@/core/server/actions/safe-action';

export const signUpWithEmailPasswordAction = rateLimitedActionClient
  .schema(emailPasswordSignUpFormSchema)
  .metadata({ name: 'email-password-sign-up' })
  .action(async ({ parsedInput: data }) => {
    const supabase = await getSupabaseServerClient();
    const supabaseAdmin = await getSupabaseServerClient({ admin: true });

    const { email, password, captchaToken, emailRedirectTo, isClient } = data;
    const { error, data: userData } = await supabase.auth.signUp({
      email,
      password,
      options: {
        captchaToken,
        emailRedirectTo,
      },
    });

    if (error || !userData.user) {
      logger.error({ error, email }, '❌ ERROR SIGNING UP USER!');
      throw error;
    }

    const identities = userData.user.identities ?? [];
    if (identities.length === 0) {
      throw new Error('❌ USER ALREADY REGISTERED!');
    }

    const userId = userData.user.id;
    const userAppMetadata = userData.user.app_metadata;

    const { error: updateError } =
      await supabaseAdmin.auth.admin.updateUserById(userId, {
        app_metadata: {
          ...userAppMetadata,
          isClient,
          termsAccepted: true,
        },
      });

    if (updateError) {
      logger.error({ updateError, userId }, '❌ ERROR SETTING USER METADATA');
      throw error;
    }

    logger.info({ email: data.email }, '✅ USER SUCCESSFULLY SIGNED UP!');

    return userId;
  });

export const signInWithEmailPasswordAction = rateLimitedActionClient
  .schema(emailPasswordSignInFormSchema)
  .metadata({ name: 'sign-in-with-email-password' })
  .action(async ({ parsedInput: { email, password, captchaToken } }) => {
    const supabase = await getSupabaseServerClient();

    const { error, data: userData } = await supabase.auth.signInWithPassword({
      email,
      password,
      options: {
        captchaToken,
      },
    });

    if (error || !userData.user) {
      logger.error({ error, email }, '❌ ERROR SIGNING IN USER');
      throw error;
    }

    logger.info({ email }, '✅ USER SIGNED IN');

    const isNotOnboarded = !userData.user.app_metadata?.isOnboarded;
    if (isNotOnboarded) {
      permanentRedirect(SiteConfig.paths.onboarding);
    }

    permanentRedirect(SiteConfig.paths.app.dashboard);
  });

export const signInWithOtpAction = rateLimitedActionClient
  .schema(emailOtpFormSchema)
  .metadata({ name: 'sign-in-with-otp' })
  .action(async ({ parsedInput: data }) => {
    const supabase = await getSupabaseServerClient();

    const { email, shouldCreateUser, captchaToken, emailRedirectTo } = data;

    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        captchaToken,
        shouldCreateUser,
        emailRedirectTo,
      },
    });

    if (error) {
      if (shouldIgnoreError(error)) {
        logger.warn(`[Development] Muted Error: ${error.message}`);
        return {} as never;
      }

      logger.error({ error, email }, '❌ ERROR SIGNING IN USING OTP');
      throw error;
    }

    logger.info({ email }, '✅ SUCCESSFULLY SIGNED IN USING OTP');
  });

export const verifyOtpAction = rateLimitedActionClient
  .schema(verifyOtpSchema)
  .metadata({ name: 'verify-otp' })
  .action(async ({ parsedInput: data }) => {
    const supabase = await getSupabaseServerClient();
    const supabaseAdmin = await getSupabaseServerClient({ admin: true });

    const { email, token, type, redirectTo, isClient } = data;

    const { error, data: userData } = await supabase.auth.verifyOtp({
      email,
      token,
      type,
      options: { redirectTo },
    });

    if (error || !userData.user) {
      logger.error({ error, email }, '❌ ERROR VERIFYING OTP!');
      throw error;
    }

    const userId = userData.user.id;
    const userAppMetadata = userData.user.app_metadata;
    const hasIsClientMetadata =
      userData.user.app_metadata?.isClient !== undefined;

    const isClientMetadata = !hasIsClientMetadata &&
      isClient !== undefined && { isClient };

    const { error: updateError } =
      await supabaseAdmin.auth.admin.updateUserById(userId, {
        app_metadata: {
          ...userAppMetadata,
          ...isClientMetadata,
          termsAccepted: true,
        },
      });

    if (updateError) {
      logger.error({ updateError, userId }, '❌ ERROR SETTING USER METADATA');
      throw error;
    }

    logger.info({ email }, '✅ OTP SUCCESSFULLY VERIFIED!');

    const isNotOnboarded = !userAppMetadata?.isOnboarded;
    if (isNotOnboarded) {
      permanentRedirect(SiteConfig.paths.onboarding);
    }
    permanentRedirect(SiteConfig.paths.app.dashboard);
  });

export const signInWithOAuthProviderAction = rateLimitedActionClient
  .schema(oAuthProviderFormSchema)
  .metadata({ name: 'sign-in-with-oauth-provider' })
  .action(async ({ parsedInput: { provider, redirectTo } }) => {
    const supabase = await getSupabaseServerClient();

    const { error, data: response } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo,
      },
    });

    if (error) {
      logger.error({ error }, '❌ ERROR SIGNING IN USER WITH OAUTH PROVIDER');
      throw error;
    }

    logger.info({ response }, '✅ USER REQUESTED OAUTH PROVIDER SIGN IN');

    redirect(response.url);
  });

export const requestPasswordResetAction = rateLimitedActionClient
  .schema(requestPasswordResetFormSchema)
  .metadata({ name: 'request-password-reset' })
  .action(async ({ parsedInput: data }) => {
    const supabase = await getSupabaseServerClient();
    const { email, redirectTo, captchaToken } = data;

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      captchaToken,
      redirectTo,
    });

    if (error) {
      logger.error({ error, email }, '❌ ERROR REQUESTING PASSWORD RESET');
      throw error;
    }

    logger.info({ email }, '✅ SUCCESSFULLY REQUESTED PASSWORD RESET');
  });

export const resendLinkAction = rateLimitedActionClient
  .schema(resendLinkFormSchema)
  .metadata({ name: 'resend-link' })
  .action(async ({ parsedInput: { email } }) => {
    const supabase = await getSupabaseServerClient();

    const { error } = await supabase.auth.resend({
      email,
      type: 'signup',
    });

    if (error) {
      logger.error({ error, email }, '❌ ERROR RESENDING LINK');
      throw error;
    }

    logger.info({ email }, '✅ SUCCESSFULLY RESENT LINK');
  });

export const verifyMfaChallengeAction = rateLimitedActionClient
  .schema(verifyMfaChallengeBodySchema)
  .metadata({ name: 'verify-mfa-challenge' })
  .action(async ({ parsedInput: data }) => {
    const supabase = await getSupabaseServerClient();

    const { factorId, code, redirectTo } = data;

    const { error, data: response } =
      await supabase.auth.mfa.challengeAndVerify({
        factorId,
        code,
      });

    if (error || !response) {
      logger.error({ error }, '❌ ERROR VERIFYING MFA!');
      throw error;
    }

    logger.info({ response }, '✅ MFA SUCCESSFULLY VERIFIED!');
    permanentRedirect(redirectTo);
  });

// ------------------------ Helper Functions ------------------------------

function shouldIgnoreError(error: AuthError) {
  return !SiteConfig.isProduction && isSmsProviderNotSetupError(error);
}

function isSmsProviderNotSetupError(error: AuthError) {
  return error.message === 'Error sending sms: sms Provider could not be found';
}
