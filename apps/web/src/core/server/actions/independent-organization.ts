'use server';

import type { Dr<PERSON>zleSupabaseClient } from '@packages/database';

import { z } from 'zod';

import { INDEPENDENT_ORGANIZATION_NAME } from '@/core/schemas/organization';
import { authActionClient } from '@/core/server/actions/safe-action';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  createOrganization,
  updateOrganization,
} from '@packages/database/functions';
import { usersTable } from '@packages/database/tables';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { deleteLogo } from '@packages/supabase/storage/logo';
import { logger } from '@packages/utils/logger';

export const createIndependentOrganizationAction = authActionClient
  .metadata({ name: 'create-independent-organization' })
  .action(async ({ ctx: { authUser } }) => {
    const userId = authUser.id;

    const db = await createDrizzleSupabaseClient();

    const organizationId = await createOrganization(db, {
      userId,
      name: INDEPENDENT_ORGANIZATION_NAME,
    });

    logger.info(
      { userId, organizationId },
      'Successfully created independent organization'
    );

    return organizationId;
  });

const updateIndependentOrganizationFormSchema = z.object({
  id: z.string(),
  logo: z.string().url().optional(),
});

export const updateIndependentOrganizationAction = authActionClient
  .schema(updateIndependentOrganizationFormSchema)
  .metadata({ name: 'update-independent-organization' })
  .action(async ({ ctx: { supabase, authUser }, parsedInput }) => {
    const userId = authUser.id;
    const { id: organizationId, logo } = parsedInput;

    const db = await createDrizzleSupabaseClient();

    const organizationName = await generateOrganizationName(db, userId);
    await updateOrganization(db, {
      organizationId,
      name: organizationName,
    });

    if (!!logo) {
      await deleteLogo({ supabase, publicUrl: logo });
    }

    logger.info(
      { userId, organizationId },
      '✅ SUCCESSFULLY UPDATED INDEPENDENT ORGANIZATION INFORMATION'
    );

    await syncTypesenseWithSupabase(userId);
  });

// -------------------------------------------------------------------------------
//                              UTILITY FUNCTIONS
// -------------------------------------------------------------------------------

async function generateOrganizationName(
  db: DrizzleSupabaseClient,
  userId: string
) {
  return await db.transaction(async (tx) => {
    const [user] = await tx
      .select({
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
      })
      .from(usersTable)
      .where(eq(usersTable.id, userId))
      .limit(1);

    const { firstName, lastName } = user!;
    const organizationName = `${firstName} ${lastName} | ${INDEPENDENT_ORGANIZATION_NAME}`;
    return organizationName;
  });
}
