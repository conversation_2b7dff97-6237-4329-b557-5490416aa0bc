'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { updateUserAndAddress } from '@packages/database/functions';
import { usersTable } from '@packages/database/tables';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { logger } from '@packages/utils/logger';

import {
  userInfoFormSchema,
  userProfilePreferencesSchema,
} from '@/core/schemas/user';
import { authActionClient } from '@/core/server/actions/safe-action';
import { deleteUser } from '@/core/server/functions/user/delete-user';

export const updateUserProfilePreferencesAction = authActionClient
  .schema(userProfilePreferencesSchema)
  .metadata({ name: 'update-user-profile-preferences' })
  .action(
    async ({
      ctx: { authUser },
      parsedInput: { isPublic, isAvailable, unavailableUntil },
    }) => {
      const userId = authUser?.id!;
      const db = await createDrizzleSupabaseClient();

      await db.transaction(async (tx) => {
        await tx
          .update(usersTable)
          .set({
            isPublic,
            isAvailable,
            unavailableUntil: !isAvailable ? unavailableUntil : null,
          })
          .where(eq(usersTable.id, userId));
      });

      await syncTypesenseWithSupabase(userId);

      revalidatePath('/app/settings/preferences', 'page');
    }
  );

export const updateUserAndAddressAction = authActionClient
  .schema(userInfoFormSchema)
  .metadata({ name: 'update-user-and-address' })
  .action(
    async ({
      ctx: { supabase, authUser },
      parsedInput: { payRateId, ...data },
    }) => {
      const userId = authUser.id;
      const isClient = authUser.app_metadata?.isClient === true;

      const db = await createDrizzleSupabaseClient();

      await updateUserAndAddress(db, {
        userId,
        ...data,
        payRateId: !isClient ? payRateId : undefined,
      });

      const { error: authError } = await supabase.auth.updateUser({
        data: {
          firstName: data.firstName,
          lastName: data.lastName,
        },
      });

      if (authError) {
        logger.error({ authError, userId }, '❌ ERROR UPDATING AUTH USER');
      }

      logger.info({ userId }, '✅ SUCCESSFULLY UPDATED USER AND ADDRESS');

      await syncTypesenseWithSupabase(userId);

      revalidatePath('/', 'layout');
    }
  );

export const deleteUserAccountAction = authActionClient
  .metadata({ name: 'delete-user-account' })
  .action(async ({ ctx: { supabase, authUser } }) => {
    const userId = authUser.id;
    const email = authUser.email;

    try {
      await deleteUser({
        supabase,
        userId,
        email,
        sendEmail: true,
      });

      await supabase.auth.signOut();

      logger.info(
        { userId },
        'User requested to delete their account, account deletion email sent'
      );

      redirect('/');
    } catch (error) {
      logger.error({ userId, error }, 'Error deleting user account');
      throw new Error();
    }
  });
