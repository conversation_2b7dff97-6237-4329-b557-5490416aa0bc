import type { EmailOtpType, Provider } from '@supabase/supabase-js';
import { z } from 'zod';

import { isValidClientEmail } from '@packages/utils/client-email-validation';

// Helper Functions
function doesPasswordMatch(params: {
  password: string;
  confirmPassword: string;
}) {
  return params.password === params.confirmPassword;
}

function isTermsAccepted(params: {
  shouldCreateUser?: boolean;
  termsAccepted?: boolean;
}) {
  return !!params.shouldCreateUser ? params.termsAccepted : true;
}

function doesInvitationEmailMatch(params: {
  email: string;
  invitationEmail?: string;
}) {
  if (!params.invitationEmail) return true;
  return params.email === params.invitationEmail;
}

const CAPTCHA_ERROR_MESSAGE =
  'Please verify that you are human by completing the captcha.';

// Schemas
const passwordSchema = z.string().min(8, 'Must be at least 8 characters');

export const emailPasswordSignInFormSchema = z
  .object({
    email: z.string().email('Invalid email address'),
    password: z.string().min(1, 'Password is required'),
    captchaToken: z.string().min(1, CAPTCHA_ERROR_MESSAGE),
    invitationEmail: z.string().email().optional(),
  })
  .refine(doesInvitationEmailMatch, {
    message: 'Email does not match the invitation email',
    path: ['email'],
  });

export const emailPasswordSignUpFormSchema = z
  .object({
    email: z.string().email(),
    password: passwordSchema,
    confirmPassword: passwordSchema,
    termsAccepted: z.boolean().refine(Boolean, {
      message: 'Please accept the Terms of Service to continue.',
    }),
    isClient: z.boolean({ required_error: 'Please select your role' }),
    captchaToken: z.string().min(1, CAPTCHA_ERROR_MESSAGE),
    emailRedirectTo: z.string().optional(),
    invitationEmail: z.string().email().optional(),
  })
  .refine(isValidClientEmail, {
    message:
      'Public emails are not allowed for clients! Try using your company email.',
    path: ['email'],
  })
  .refine(doesInvitationEmailMatch, {
    message: 'Email does not match the invitation email',
    path: ['email'],
  })
  .refine(doesPasswordMatch, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

export const emailOtpFormSchema = z
  .object({
    email: z.string().email(),
    invitationEmail: z.string().email().optional(),
    termsAccepted: z.boolean().optional(),
    isClient: z.boolean().optional(),
    captchaToken: z.string().min(1, CAPTCHA_ERROR_MESSAGE),
    shouldCreateUser: z.boolean().optional(),
    emailRedirectTo: z.string().url().optional(),
  })
  .refine(isTermsAccepted, {
    message: 'Please accept the Terms of Service to continue.',
    path: ['termsAccepted'],
  })
  .refine(isValidClientEmail, {
    message:
      'Public emails are not allowed for clients! Try using your company email.',
    path: ['email'],
  })
  .refine(doesInvitationEmailMatch, {
    message: 'Email does not match the invitation email',
    path: ['email'],
  });

export const verifyOtpSchema = z.object({
  email: z.string().email(),
  token: z.string(),
  type: z.custom<EmailOtpType>(),
  isClient: z.boolean().optional(),
  redirectTo: z.string().url().optional(),
});

export const oAuthProviderFormSchema = z.object({
  provider: z.custom<Provider>(),
  redirectTo: z.string().url().optional(),
});

export const requestPasswordResetFormSchema = z.object({
  email: z.string().email(),
  captchaToken: z.string().min(1, CAPTCHA_ERROR_MESSAGE),
  redirectTo: z.string().url().optional(),
});

export const passwordResetFormSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: passwordSchema,
  })
  .refine(doesPasswordMatch, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

export const resendLinkFormSchema = z.object({
  email: z.string().email(),
});

export const verifyMfaChallengeBodySchema = z.object({
  factorId: z.string(),
  code: z.string(),
  redirectTo: z.string(),
});

// Types
export type EmailPasswordSignInFormValues = z.infer<
  typeof emailPasswordSignInFormSchema
>;

export type EmailPasswordSignUpFormValues = z.infer<
  typeof emailPasswordSignUpFormSchema
>;

export type EmailOtpFormValues = z.infer<typeof emailOtpFormSchema>;

export type RequestPasswordResetFormValues = z.infer<
  typeof requestPasswordResetFormSchema
>;

export type PasswordResetFormValues = z.infer<typeof passwordResetFormSchema>;

export type ResendLinkFormValues = z.infer<typeof resendLinkFormSchema>;
