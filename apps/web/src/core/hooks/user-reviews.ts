import type { UserReviewResponse } from '@packages/database/types';

import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetUserReviewsByUserIdQuery({
  userId,
  enabled,
  initialData,
}: {
  userId: string;
  enabled?: boolean;
  initialData?: UserReviewResponse[];
}) {
  const fetchApi = useApiRequest<UserReviewResponse[]>();
  return useQuery({
    enabled,
    initialData,
    queryKey: ['user-reviews', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/user-reviews/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useGetGivenUserReviewsByUserIdQuery(userId: string) {
  const fetchApi = useApiRequest<UserReviewResponse[]>();
  return useQuery({
    queryKey: ['given-user-reviews', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/user-reviews/given/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
