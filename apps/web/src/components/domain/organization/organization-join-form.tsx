'use client';

import type { Organization } from '@packages/supabase/organization';

import {
  ArrowLeftIcon,
  CheckCircledIcon,
  CrossCircledIcon,
  UpdateIcon,
} from '@radix-ui/react-icons';
import Link from 'next/link';
import { useTransition } from 'react';
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from '@packages/ui/avatar';
import { Badge } from '@packages/ui/badge';
import { Button, buttonVariants } from '@packages/ui/button';
import { Card, CardTitle } from '@packages/ui/card';
import { ReadMoreContainer } from '@packages/ui/read-more-container';
import { Skeleton } from '@packages/ui/skeleton';

import { SiteConfig } from '@/configuration';
import { useGetOrgJoinRequestStatusByUserIdQuery } from '@/core/hooks/organization';
import { useUserSession } from '@/core/hooks/user';
import { createOrganizationJoinRequestAction } from '@/core/server/actions/organization-join-request';

interface OrganizationJoinFormProps {
  organization?: Organization;
  onContinue?: VoidFunction;
}

export function OrganizationJoinForm({
  organization,
  onContinue,
}: OrganizationJoinFormProps) {
  const userSession = useUserSession();
  const currentUserId = userSession?.user?.id!;

  const [isLoading, startTransition] = useTransition();

  const {
    data: orgJoinRequest,
    refetch,
    isPending,
  } = useGetOrgJoinRequestStatusByUserIdQuery(currentUserId);

  const handleRequestJoinOrg = () => {
    startTransition(async () => {
      const promise = createOrganizationJoinRequestAction({
        organizationId: organization?.id!,
      });

      toast.promise(promise, {
        loading: 'Requesting to join organization...',
        success: () => {
          refetch();
          return 'Request sent successfully';
        },
        error: (error) =>
          `Failed to request to join organization: ${error.message ?? ''}`,
      });
    });
  };

  if (isPending) {
    return <OrganizationJoinFormSkeleton />;
  }

  if (orgJoinRequest?.status === 'APPROVED') {
    return (
      <OrganizationJoinFormApproved
        organization={organization}
        onContinue={onContinue}
      />
    );
  }

  if (orgJoinRequest?.status === 'PENDING') {
    return <OrganizationJoinFormPending organization={organization} />;
  }

  if (orgJoinRequest?.status === 'REJECTED') {
    return <OrganizationJoinFormRejected />;
  }

  return (
    <Card className="w-full max-w-lg flex flex-col items-center p-8 space-y-6 shadow-none">
      <Avatar className="size-32 ring-2 ring-offset-2 ring-primary/10">
        <AvatarImage
          src={organization?.logo ?? undefined}
          alt={organization?.name}
        />
        <AvatarFallback>{organization?.name?.slice(0, 2)}</AvatarFallback>
      </Avatar>

      <div className="space-y-2 text-center">
        <CardTitle className="text-2xl font-bold">
          {organization?.name}
        </CardTitle>

        <ReadMoreContainer className="text-sm text-muted-foreground">
          {organization?.description}
        </ReadMoreContainer>
      </div>

      <Button
        onClick={handleRequestJoinOrg}
        className="w-full"
        disabled={isPending || isLoading}
      >
        Request to join
      </Button>
    </Card>
  );
}

function OrganizationJoinFormSkeleton() {
  return (
    <Card className="w-full max-w-lg flex flex-col items-center gap-6 p-8 shadow-none">
      <Skeleton className="size-32 rounded-full" />
      <div className="w-full flex flex-col items-center gap-4">
        <Skeleton className="h-6 w-2/3" />
        <div className="w-full flex flex-col items-center gap-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/5" />
          <Skeleton className="h-4 w-4/5" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      </div>
      <Skeleton className="h-9 w-full" />
    </Card>
  );
}

function OrganizationJoinFormApproved(props: {
  organization?: Organization;
  onContinue?: VoidFunction;
}) {
  return (
    <Card className="w-full max-w-lg flex flex-col justify-center items-center gap-6 text-center p-8 shadow-none">
      <Avatar className="size-24 ring-2 ring-offset-2 ring-primary/10">
        <AvatarImage
          alt={props.organization?.name}
          src={props.organization?.logo ?? undefined}
        />
        <AvatarFallback>{props.organization?.name?.slice(0, 2)}</AvatarFallback>
      </Avatar>

      <div className="space-y-2 text-center">
        <CardTitle className="text-2xl font-bold">
          {props.organization?.name}
        </CardTitle>

        <Badge
          variant="secondary"
          className="w-fit text-sm rounded-full px-2 py-1 font-medium bg-green-400/20 text-green-600 dark:text-green-400 hover:bg-green-400/20"
        >
          <CheckCircledIcon className="size-4 mr-1" />
          <span>Request Approved</span>
        </Badge>

        <ReadMoreContainer className="text-sm text-muted-foreground">
          {props.organization?.description}
        </ReadMoreContainer>
      </div>

      <Button onClick={props.onContinue} className="w-full">
        Continue
      </Button>
    </Card>
  );
}

function OrganizationJoinFormPending(props: { organization?: Organization }) {
  return (
    <Card className="w-full max-w-lg flex flex-col items-center gap-3 text-center p-8 shadow-none">
      <Avatar className="size-24 ring-2 ring-offset-2 ring-primary/10 mb-3">
        <AvatarImage
          src={props.organization?.logo ?? undefined}
          alt={props.organization?.name}
        />
        <AvatarFallback>{props.organization?.name?.slice(0, 2)}</AvatarFallback>
      </Avatar>

      <CardTitle className="text-2xl font-bold">
        {props.organization?.name}
      </CardTitle>

      <Badge
        variant="secondary"
        className="w-fit text-sm rounded-full px-2 py-1 font-medium bg-yellow-400/20 text-yellow-600 dark:text-yellow-400 hover:bg-yellow-400/20"
      >
        <UpdateIcon className="size-4 mr-1" />
        <span>Request Pending</span>
      </Badge>

      <p className="text-sm text-muted-foreground">
        Your request to join the organization has been sent. You'll be notified
        once it's reviewed.
      </p>
    </Card>
  );
}

function OrganizationJoinFormRejected() {
  return (
    <Card className="w-full max-w-lg flex flex-col items-center gap-3 text-center p-8 shadow-none">
      <div className="size-24 rounded-full bg-red-50 dark:bg-red-200 grid place-items-center mb-3">
        <CrossCircledIcon className="size-12 text-red-500" />
      </div>

      <Badge
        variant="secondary"
        className="w-fit text-sm rounded-full px-2 py-1 font-medium bg-red-400/20 text-red-600 dark:text-red-400 hover:bg-red-400/20"
      >
        <CrossCircledIcon className="size-4 mr-1" />
        <span>Request Not Approved</span>
      </Badge>

      <p className="text-sm text-muted-foreground">
        Unfortunately, your request to join the organization was not approved at
        this time.
      </p>

      <Link
        href={SiteConfig.paths.root}
        className={buttonVariants({ className: 'w-full' })}
      >
        <ArrowLeftIcon className="size-4 mr-2" />
        <span>Go Back Home</span>
      </Link>
    </Card>
  );
}
